import numpy as np 

def svd_2x2_singular_values(A: np.n<PERSON><PERSON>) -> tuple:
    """
    finds an approximate singular value decomposition of a real 2 x 2 matrix using one Jacob<PERSON> rotation. 
    Input A: a NumPy array of shape (2, 2)

    Rules You may use basic NumPy operations (matrix multiplication, transpose, element wise math, etc.). 
    Do not call numpy.linalg.svd or any other high-level SVD routine. Stick to a single Jacobi step no iterative refinements.

    Return A tuple (U, Î£, V_T) where U is a 2 x 2 orthogonal matrix, Î£ is a length 2 NumPy array containing the singular values, 
    and V_T is the transpose of the right-singular-vector matrix V.

    U is the first matrix 
    sigma is the second vector 
    and V is the third matrix
    """
    # Input validation
    if not isinstance(A, np.ndarray):
        raise TypeError("Input must be a numpy array")
    if A.shape != (2, 2):
        raise ValueError("Input matrix must be 2x2")
    
    # Compute A^T * A to get the right singular vectors
    ATA = A.T @ A
    
    # Compute the eigenvalues and eigenvectors of A^T * A
    # For 2x2 matrix, we can compute this directly
    a = ATA[0, 0]
    b = ATA[0, 1]
    c = ATA[1, 1]
    
    # Compute eigenvalues
    trace = a + c
    det = a * c - b * b
    delta = np.sqrt(trace * trace - 4 * det)
    lambda1 = (trace + delta) / 2
    lambda2 = (trace - delta) / 2
    
    # Compute singular values and ensure they are in descending order
    sigma = np.array([np.sqrt(lambda1), np.sqrt(lambda2)])
    if sigma[0] < sigma[1]:
        sigma = sigma[::-1]
        lambda1, lambda2 = lambda2, lambda1
    
    # Compute right singular vectors (eigenvectors of A^T * A)
    if abs(b) < 1e-10:
        V = np.eye(2)
    else:
        # Compute eigenvectors
        v1 = np.array([b, lambda1 - a])
        v2 = np.array([b, lambda2 - a])
        # Normalize
        v1 = v1 / np.linalg.norm(v1)
        v2 = v2 / np.linalg.norm(v2)
        V = np.column_stack((v1, v2))
    
    # Enforce a sign convention on V to be a rotation matrix (det(V) = 1)
    if np.linalg.det(V) < 0:
        V[:, 1] = -V[:, 1]

    # Compute left singular vectors using U = AVΣ^(-1)
    U = A @ V
    # Normalize each column by corresponding singular value
    for i in range(2):
        U[:, i] = U[:, i] / sigma[i]
    
    # Ensure orthogonality
    if abs(np.dot(U[:, 0], U[:, 1])) > 1e-10:
        # Gram-Schmidt orthogonalization
        U[:, 1] = U[:, 1] - np.dot(U[:, 0], U[:, 1]) * U[:, 0]
        U[:, 1] = U[:, 1] / np.linalg.norm(U[:, 1])
    
    return U, sigma, V.T


if __name__ == "__main__":
    print("--- Test Case 1 ---")
    print(svd_2x2_singular_values(np.array([[2, 1], [1, 2]])))
    print("\n--- Test Case 2 ---")
    print(svd_2x2_singular_values(np.array([[1, 2], [3, 4]])))