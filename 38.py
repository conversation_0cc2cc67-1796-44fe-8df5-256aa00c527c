import numpy as np
import math

def adaboost_fit(X, y, n_clf):
	"""Write a Python function adaboost_fit that implements the fit method for an AdaBoost classifier. The function should take in a 2D numpy array X of shape (n_samples, n_features) representing the dataset, a 1D numpy array y of shape (n_samples,) representing the labels, and an integer n_clf representing the number of classifiers. The function should initialize sample weights, find the best thresholds for each feature, calculate the error, update weights, and return a list of classifiers with their parameters.
    
	Example:
    Input:

    X = np.array([[1, 2], [2, 3], [3, 4], [4, 5]])
        y = np.array([1, 1, -1, -1])
        n_clf = 3

        clfs = adaboost_fit(X, y, n_clf)
        print(clfs)

    Output:

    (example format, actual values may vary):
        # [{'polarity': 1, 'threshold': 2, 'feature_index': 0, 'alpha': 0.5},
        #  {'polarity': -1, 'threshold': 3, 'feature_index': 1, 'alpha': 0.3},
        #  {'polarity': 1, 'threshold': 4, 'feature_index': 0, 'alpha': 0.2}]

    Reasoning:

    The function fits an AdaBoost classifier on the dataset X with the given labels y and number of classifiers n_clf. It returns a list of classifiers with their parameters, including the polarity, threshold, feature index, and alpha values
    """
	n_samples, n_features = np.shape(X)
	w = np.full(n_samples, (1 / n_samples))
	clfs = []

	# Your code here
	for t in range(n_clf):
		# Find the best weak classifier (decision stump)
		best_clf = None
		best_error = float('inf')
		
		for feature_idx in range(n_features):
			feature_values = X[:, feature_idx]
			unique_values = np.unique(feature_values)
			
			# Try different thresholds
			for threshold in unique_values:
				# Try both polarities
				for polarity in [-1, 1]:
					# Make predictions
					predictions = np.where(feature_values >= threshold, polarity, -polarity)
					
					# Calculate weighted error
					error = np.sum(w * (predictions != y))
					
					# Update best classifier if error is smaller
					if error < best_error:
						best_error = error
						best_clf = {
							'polarity': polarity,
							'threshold': threshold,
							'feature_index': feature_idx,
						}
		
		# If error is 0 or >= 0.5, break
		if best_error >= 0.5:
			best_error = 1 - best_error
			best_clf = {
				'polarity': -1 * polarity,
				'threshold': threshold,
				'feature_index': feature_idx,
			}
		
		# Calculate alpha (classifier weight)
		alpha = 0.5 * np.log((1 - best_error) / (best_error + 1e-10))
		best_clf['alpha'] = alpha
		
		# Make predictions with best classifier
		feature_values = X[:, best_clf['feature_index']]
		predictions = np.where(feature_values >= best_clf['threshold'], 
							  best_clf['polarity'], -best_clf['polarity'])
		
		# Update sample weights
		w = w * np.exp(-alpha * y * predictions)
		w = w / np.sum(w)  # Normalize weights
		
		# Add classifier to list
		clfs.append(best_clf)

	return clfs
