import numpy as np

def train_neuron(features: np.ndarray, labels: np.ndarray, initial_weights: np.ndarray, initial_bias: float, learning_rate: float, epochs: int) -> (np.ndarray, float, list[float]):
	"""simulates a single neuron with sigmoid activation, and implements backpropagation to update the neuron's weights and bias. The function should take a list of feature vectors, associated true binary labels, initial weights, initial bias, a learning rate, and the number of epochs. The function should update the weights and bias using gradient descent based on the MSE loss, and return the updated weights, bias, and a list of MSE values for each epoch, each rounded to four decimal places.
    
	Example:
    Input:

    features = [[1.0, 2.0], [2.0, 1.0], [-1.0, -2.0]], labels = [1, 0, 0], initial_weights = [0.1, -0.2], initial_bias = 0.0, learning_rate = 0.1, epochs = 2

    Output:

    updated_weights = [0.1036, -0.1425], updated_bias = -0.0167, mse_values = [0.3033, 0.2942]

    Reasoning:

    The neuron receives feature vectors and computes predictions using the sigmoid activation. Based on the predictions and true labels, the gradients of MSE loss with respect to weights and bias are computed and used to update the model parameters across epochs.
    """
	# Convert inputs to numpy arrays if they aren't already
	features = np.array(features)
	labels = np.array(labels)
	weights = np.array(initial_weights)
	bias = initial_bias
	
	mse_values = []
	
	# Sigmoid activation function
	def sigmoid(x):
		return 1 / (1 + np.exp(-x))
	
	# Sigmoid derivative
	def sigmoid_derivative(x):
		return x * (1 - x)
	
	for epoch in range(epochs):
		total_mse = 0
		weight_gradients = np.zeros_like(weights)
		bias_gradient = 0
		
		# Forward pass and compute gradients for each sample
		for i in range(len(features)):
			# Forward pass
			z = np.dot(features[i], weights) + bias
			prediction = sigmoid(z)
			
			# Compute MSE loss
			mse = (prediction - labels[i]) ** 2
			total_mse += mse
			
			# Backpropagation
			# Gradient of MSE with respect to prediction
			d_mse_d_pred = 2 * (prediction - labels[i])
			
			# Gradient of prediction with respect to z
			d_pred_d_z = sigmoid_derivative(prediction)
			
			# Gradient of z with respect to weights and bias
			d_z_d_weights = features[i]
			d_z_d_bias = 1
			
			# Chain rule to get gradients
			d_mse_d_weights = d_mse_d_pred * d_pred_d_z * d_z_d_weights
			d_mse_d_bias = d_mse_d_pred * d_pred_d_z * d_z_d_bias
			
			# Accumulate gradients
			weight_gradients += d_mse_d_weights
			bias_gradient += d_mse_d_bias
		
		# Update weights and bias using gradient descent
		weights -= learning_rate * weight_gradients / len(features)
		bias -= learning_rate * bias_gradient / len(features)
		
		# Store average MSE for this epoch
		avg_mse = total_mse / len(features)
		mse_values.append(round(avg_mse, 4))
	
	return weights, bias, mse_values