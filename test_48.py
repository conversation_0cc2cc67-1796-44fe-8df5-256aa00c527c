import numpy as np
from importlib import import_module

# Import the rref function
module = import_module('48')
rref = module.rref

def test_rref_example():
    matrix = np.array([
        [1, 2, -1, -4],
        [2, 3, -1, -11],
        [-2, 0, -3, 22]
    ])

    expected_rref_matrix = np.array([
        [ 1.,  0.,  0., -8.],
        [ 0.,  1.,  0.,  1.],
        [ 0.,  0.,  1., -2.]
    ])

    rref_matrix = rref(matrix)
    np.testing.assert_allclose(rref_matrix, expected_rref_matrix, atol=1e-9)

def test_rref_identity_matrix():
    matrix = np.array([
        [1, 0, 0],
        [0, 1, 0],
        [0, 0, 1]
    ])
    expected_rref_matrix = np.array([
        [1., 0., 0.],
        [0., 1., 0.],
        [0., 0., 1.]
    ])
    rref_matrix = rref(matrix)
    np.testing.assert_allclose(rref_matrix, expected_rref_matrix, atol=1e-9)

def test_rref_zero_matrix():
    matrix = np.array([
        [0, 0],
        [0, 0]
    ])
    expected_rref_matrix = np.array([
        [0., 0.],
        [0., 0.]
    ])
    rref_matrix = rref(matrix)
    np.testing.assert_allclose(rref_matrix, expected_rref_matrix, atol=1e-9)

def test_rref_singular_matrix():
    matrix = np.array([
        [1, 2],
        [2, 4]
    ])
    expected_rref_matrix = np.array([
        [1., 2.],
        [0., 0.]
    ])
    rref_matrix = rref(matrix)
    np.testing.assert_allclose(rref_matrix, expected_rref_matrix, atol=1e-9)

def test_rref_user_provided_case():
    matrix = np.array([
        [1, 2, -1],
        [2, 4, -1],
        [-2, -4, -3]
    ])
    expected_rref_matrix = np.array([
        [ 1., 2., 0.],
        [ 0., 0., 1.],
        [ 0., 0., 0.]
    ])
    rref_matrix = rref(matrix)
    np.testing.assert_allclose(rref_matrix, expected_rref_matrix, atol=1e-9)