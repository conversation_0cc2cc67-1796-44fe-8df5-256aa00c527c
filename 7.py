import numpy as np
import pytest


def transform_matrix(
    A: list[list[int | float]], T: list[list[int | float]], S: list[list[int | float]]
) -> list[list[int | float]]:
    """
    Write a Python function that transforms a given matrix A using the operation
    T−1AST−1AS, where T and S are invertible matrices.
    The function should first validate if the matrices T and S are invertible,
    and then perform the transformation. In cases where there is no solution return -1
    """
    try:
        # Convert input lists to numpy arrays
        A = np.array(A)
        T = np.array(T)
        S = np.array(S)
        # Check if matrices are square
        if T.shape[0] != T.shape[1] or S.shape[0] != S.shape[1]:
            return -1
        # Check if matrices are invertible by calculating determinant
        if np.linalg.det(T) == 0 or np.linalg.det(S) == 0:
            return -1
        # Calculate T^(-1)
        T_inv = np.linalg.inv(T)
        # Perform the transformation T^(-1)AST^(-1)AS
        result = T_inv @ A @ S
        # Convert back to list of lists
        return result.tolist()
    except np.linalg.LinAlgError:
        # Handle any linear algebra errors (e.g., singular matrix)
        return -1
    except Exception:
        # Handle any other unexpected errors
        return -1


@pytest.mark.parametrize("A, T, S, expected", [
    # Test case 1: Basic 2x2 matrices
    ([[1, 2], [3, 4]], [[2, 0], [0, 2]], [[1, 1], [0, 1]], [[0.5, 1.5], [1.5, 3.5]]),
    # Test case 2: Non-invertible matrix T
    ([[1, 2], [3, 4]], [[1, 1], [1, 1]], [[1, 0], [0, 1]], -1),
    # Test case 3: Non-square matrix
    ([[1, 2], [3, 4]], [[1, 2, 3], [4, 5, 6]], [[1, 0], [0, 1]], -1),
    # Test case 4: Identity matrices
    ([[1, 2], [3, 4]], [[1, 0], [0, 1]], [[1, 0], [0, 1]], [[1, 2], [3, 4]]),
])
def test_transform_matrix(A, T, S, expected):
    result = transform_matrix(A, T, S)
    if isinstance(expected, list):
        np.testing.assert_array_almost_equal(result, expected)
    else:
        assert result == expected
