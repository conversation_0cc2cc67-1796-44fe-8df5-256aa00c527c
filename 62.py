
import numpy as np
class SimpleRNN:
    """implement a simple Recurrent Neural Network (RNN) and backpropagation through time (BPTT) to learn from sequential data. The RNN will process input sequences, update hidden states, and perform backpropagation to adjust weights based on the error gradient.

    Write a class SimpleRNN with the following methods:

        __init__(self, input_size, hidden_size, output_size): Initializes the RNN with random weights and zero biases.
        forward(self, x): Processes a sequence of inputs and returns the hidden states and output.
        backward(self, x, y, learning_rate): Performs backpropagation through time (BPTT) to adjust the weights based on the loss.

    In this task, the RNN will be trained on sequence prediction, where the network will learn to predict the next item in a sequence. You should use 1/2 * Mean Squared Error (MSE) as the loss function and make sure to aggregate the losses at each time step by summing.
    Example:
    Input:

    import numpy as np
        input_sequence = np.array([[1.0], [2.0], [3.0], [4.0]])
        expected_output = np.array([[2.0], [3.0], [4.0], [5.0]])
        # Initialize RNN
        rnn = SimpleRNN(input_size=1, hidden_size=5, output_size=1)
        
        # Forward pass
        output = rnn.forward(input_sequence)
        
        # Backward pass
        rnn.backward(input_sequence, expected_output, learning_rate=0.01)
        
        print(output)
        
        # The output should show the RNN predictions for each step of the input sequence.

    Output:

    [[x1], [x2], [x3], [x4]]

    Reasoning:

    The RNN processes the input sequence [1.0, 2.0, 3.0, 4.0] and predicts the next item in the sequence at each step.
    """
    def __init__(self, input_size, hidden_size, output_size):
        """
        Initializes the RNN with random weights and zero biases.
        """
        # Store the size of the hidden layer
        self.hidden_size = hidden_size
        # Initialize the weight matrix for input to hidden layer connections
        self.W_xh = np.random.randn(hidden_size, input_size) * 0.01
        # Initialize the weight matrix for hidden to hidden layer connections
        self.W_hh = np.random.randn(hidden_size, hidden_size) * 0.01
        # Initialize the weight matrix for hidden to output layer connections
        self.W_hy = np.random.randn(output_size, hidden_size) * 0.01
        # Initialize the bias for the hidden layer
        self.b_h = np.zeros((hidden_size, 1))
        # Initialize the bias for the output layer
        self.b_y = np.zeros((output_size, 1))

    def forward(self, x):
        # Dictionary to store hidden states at each time step
        self.h = {}
        # Dictionary to store output predictions at each time step
        self.y_pred = {}
        # Initialize the hidden state at time step -1 with zeros
        self.h[-1] = np.zeros((self.hidden_size, 1))

        # Iterate through each time step in the input sequence
        for t in range(len(x)):
            # Use slicing x[t:t+1] to keep the input as a 2D column vector
            # This prevents broadcasting issues during matrix multiplication
            current_x = x[t:t+1]
            # Calculate the new hidden state using the input, previous hidden state, and weights
            self.h[t] = np.tanh(np.dot(self.W_xh, current_x) + np.dot(self.W_hh, self.h[t - 1]) + self.b_h)
            # Calculate the output prediction for the current time step
            self.y_pred[t] = np.dot(self.W_hy, self.h[t]) + self.b_y

        # Return a list of all output predictions
        return [self.y_pred[t] for t in range(len(x))]

    def backward(self, x, y, learning_rate):
        # Initialize gradients for weights and biases with zeros
        dW_xh, dW_hh, dW_hy = np.zeros_like(self.W_xh), np.zeros_like(self.W_hh), np.zeros_like(self.W_hy)
        db_h, db_y = np.zeros_like(self.b_h), np.zeros_like(self.b_y)
        # Initialize the gradient of the hidden state from the next time step with zeros
        dh_next = np.zeros_like(self.h[0])

        # Iterate backwards through time
        for t in reversed(range(len(x))):
            # Use slicing y[t:t+1] to keep the target as a 2D column vector
            current_y = y[t:t+1]
            # Calculate the error between the predicted output and the true output
            dy = self.y_pred[t] - current_y
            # Calculate the gradient of the hidden-to-output weights
            dW_hy += np.dot(dy, self.h[t].T)
            # Calculate the gradient of the output bias
            db_y += dy

            # Backpropagate the error to the hidden layer
            dh = np.dot(self.W_hy.T, dy) + dh_next
            # Backpropagate through the tanh activation function
            dh_raw = (1 - self.h[t] ** 2) * dh

            # Use slicing x[t:t+1] to keep the input as a 2D column vector
            current_x = x[t:t+1]
            # Calculate the gradient of the input-to-hidden weights
            dW_xh += np.dot(dh_raw, current_x.T)
            # Calculate the gradient of the hidden-to-hidden weights
            dW_hh += np.dot(dh_raw, self.h[t - 1].T)
            # Calculate the gradient of the hidden bias
            db_h += dh_raw

            # Update the gradient of the hidden state for the next (previous in time) iteration
            dh_next = np.dot(self.W_hh.T, dh_raw)

        # Clip gradients to prevent exploding gradients
        for dparam in [dW_xh, dW_hh, dW_hy, db_h, db_y]:
            np.clip(dparam, -5, 5, out=dparam)

        # Update weights and biases using the calculated gradients and learning rate
        self.W_xh -= learning_rate * dW_xh
        self.W_hh -= learning_rate * dW_hh
        self.W_hy -= learning_rate * dW_hy
        self.b_h -= learning_rate * db_h
        self.b_y -= learning_rate * db_y
