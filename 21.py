import numpy as np

def pegasos_kernel_svm(data: np.ndarray, labels: np.ndarray, kernel='linear', lambda_val=0.01, iterations=100, sigma=1.0) -> (list, float):
    """ implements a deterministic version of the Pegasos algorithm to train a kernel SVM classifier from scratch. 
    The function should take a dataset (as a 2D NumPy array where each row represents a data sample and each column represents a feature), 
    a label vector (1D NumPy array where each entry corresponds to the label of the sample), and training parameters such as the choice of kernel (linear or RBF), 
    regularization parameter (lambda), and the number of iterations. Note that while the original Pegasos algorithm is stochastic (it selects a single random sample at each step), 
    this problem requires using all samples in every iteration (i.e., no random sampling). The function should perform binary classification and return the model's alpha coefficients and bias.
    
    Reasoning:

    Using the RBF kernel, the Pegasos algorithm iteratively updates the weights based on a sub-gradient descent method, 
    taking into account the non-linear separability of the data induced by the kernel transformation.
    """
    n_samples = data.shape[0]
    
    # Initialize alpha coefficients and bias
    alphas = np.zeros(n_samples)
    b = 0.0
    
    # Compute kernel matrix
    def compute_kernel(x1, x2):
        if kernel == 'linear':
            return np.dot(x1, x2)
        elif kernel == 'rbf':
            return np.exp(-np.sum((x1 - x2) ** 2) / (2 * sigma ** 2))
    
    K = np.zeros((n_samples, n_samples))
    for i in range(n_samples):
        for j in range(n_samples):
            K[i, j] = compute_kernel(data[i], data[j])
    
    # Training loop
    for t in range(1, iterations + 1):
        learning_rate = 1.0 / (lambda_val * t)
        
        # First, apply regularization to all alphas
        # alphas = (1 - learning_rate * lambda_val) * alphas
        
        # Sequentially update for each sample
        for i in range(n_samples):
            # Compute prediction for current sample using current alphas and b
            prediction = np.sum(alphas * labels * K[i]) + b
            
            # Update if margin is violated
            if labels[i] * prediction < 1:
                alphas[i] *= (1 - learning_rate * lambda_val)
                alphas[i] += learning_rate * labels[i]
                # Update bias with a larger step size for RBF kernel
                b += learning_rate * labels[i]
        
    # Round to match expected output format
    alphas = np.round(alphas, 4)
    b = np.round(b, 4)
        
    return alphas.tolist(), b

# data = np.array([[1, 2], [2, 3], [3, 1], [4, 1]]), labels = np.array([1, 1, -1, -1]), kernel = 'rbf', lambda_val = 0.01, iterations = 100, sigma = 1.0

print(pegasos_kernel_svm(np.array([[1, 2], [2, 3], [3, 1], [4, 1]]), np.array([1, 1, -1, -1]), kernel='rbf', lambda_val=0.01, iterations=100))