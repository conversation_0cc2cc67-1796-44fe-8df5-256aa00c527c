import numpy as np
import importlib.util
import sys

# Import the svd_2x2 function from 28.py
spec = importlib.util.spec_from_file_location("module_28", "28.py")
module_28 = importlib.util.module_from_spec(spec)
sys.modules["module_28"] = module_28
spec.loader.exec_module(module_28)
svd_2x2 = module_28.svd_2x2

# Test matrix from the docstring
A = np.array([[-10, 8], [10, -1]])

# Expected results from docstring
expected_U = np.array([[0.8, -0.6], [-0.6, -0.8]])
expected_S = np.array([15.65247584, 4.47213595])
expected_V = np.array([[-0.89442719, 0.4472136], [-0.4472136, -0.89442719]])

# Our results
U, S, V = svd_2x2(A)

print("Expected U:")
print(expected_U)
print("Actual U:")
print(U)
print("U difference:", np.linalg.norm(U - expected_U))
print()

print("Expected S:")
print(expected_S)
print("Actual S:")
print(S)
print("S difference:", np.linalg.norm(S - expected_S))
print()

print("Expected V:")
print(expected_V)
print("Actual V:")
print(V)
print("V difference:", np.linalg.norm(V - expected_V))
print()

# Verify the decomposition works
reconstructed = U @ np.diag(S) @ V
print("Original A:")
print(A)
print("Reconstructed A:")
print(reconstructed)
print("Reconstruction error:", np.linalg.norm(A - reconstructed)) 