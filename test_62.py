import numpy as np
from sixty_two import SimpleRNN

def test_simple_rnn():
    np.random.seed(42)
    input_sequence = np.array([[1.0], [2.0], [3.0], [4.0]])
    expected_output = np.array([[2.0], [3.0], [4.0], [5.0]])
    rnn = SimpleRNN(input_size=1, hidden_size=5, output_size=1)
    for epoch in range(1000):
        output = rnn.forward(input_sequence)
        rnn.backward(input_sequence, expected_output, learning_rate=0.01)
    
    expected = [[[2.24143915]],[[3.18450265]],[[4.04305928]],[[4.57419398]]]
    np.testing.assert_allclose(output, expected, rtol=1e-5, atol=1e-5)