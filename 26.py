class Value:
	"""implements the basic autograd operations: addition, multiplication, and ReLU activation. The class should handle scalar values and should correctly compute gradients for these operations through automatic differentiation.
    
	Example:
    Input:

        a = Value(2)
        b = Value(-3)
        c = Value(10)
        d = a + b * c
        e = d.relu()
        e.backward()
        print(a, b, c, d, e)

    Output:

        Value(data=2, grad=0) Value(data=-3, grad=0) Value(data=10, grad=0)

    Reasoning:

    The output reflects the forward computation and gradients after backpropagation. The ReLU on 'd' zeros out its output and gradient due to the negative data value.
    """
	def __init__(self, data, _children=(), _op=''):
		self.data = data
		self.grad = 0
		self._backward = lambda: None
		self._prev = set(_children)
		self._op = _op
	def __repr__(self):
		return f"Value(data={self.data}, grad={self.grad})"

	def __add__(self, other):
		# Convert other to Value if it's not already
		other = other if isinstance(other, Value) else Value(other)
		out = Value(self.data + other.data, (self, other), '+')
		
		def _backward():
			self.grad += out.grad
			other.grad += out.grad
		out._backward = _backward
		
		return out

	def __mul__(self, other):
		# Convert other to Value if it's not already
		other = other if isinstance(other, Value) else Value(other)
		out = Value(self.data * other.data, (self, other), '*')
		
		def _backward():
			self.grad += other.data * out.grad
			other.grad += self.data * out.grad
		out._backward = _backward
		
		return out

	def relu(self):
		out = Value(0 if self.data < 0 else self.data, (self,), 'ReLU')
		
		def _backward():
			self.grad += (out.data > 0) * out.grad
		out._backward = _backward
		
		return out

	def backward(self):
		# Topological sort all nodes in the graph
		topo = []
		visited = set()
		
		def build_topo(v):
			if v not in visited:
				visited.add(v)
				for child in v._prev:
					build_topo(child)
				topo.append(v)
		
		build_topo(self)
		
		# Go one variable at a time and apply the chain rule to get its gradient
		self.grad = 1
		for v in reversed(topo):
			v._backward()