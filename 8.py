def inverse_2x2(matrix: list[list[float]]) -> list[list[float]]:
	"""
	calculates the inverse of a 2x2 matrix. Return 'None' if the matrix is not invertible.
	
	The inverse of a 2x2 matrix [a, b], [c, d] is given by (1/(ad-bc)) * [d, -b], [-c, a], provided ad-bc is not zero.
	"""
	# Extract matrix elements
	a, b = matrix[0]
	c, d = matrix[1]
	
	# Calculate determinant
	determinant = a * d - b * c
	
	# Check if matrix is invertible
	if determinant == 0:
		return None
	
	# Calculate inverse
	inverse = [
		[d/determinant, -b/determinant],
		[-c/determinant, a/determinant]
	]
	
	return inverse


import unittest
from typing import List
from typing import Optional
from typing import Union

class TestInverse2x2(unittest.TestCase):
    def test_normal_invertible_matrix(self):
        # Test case 1: Normal invertible matrix
        matrix = [[1, 2], [3, 4]]
        expected = [[-2.0, 1.0], [1.5, -0.5]]
        result = inverse_2x2(matrix)
        self.assertEqual(result, expected)
        
        # Test case 2: Another invertible matrix
        matrix = [[2, 1], [1, 1]]
        expected = [[1.0, -1.0], [-1.0, 2.0]]
        result = inverse_2x2(matrix)
        self.assertEqual(result, expected)

    def test_non_invertible_matrix(self):
        # Test case: Matrix with determinant = 0
        matrix = [[1, 2], [2, 4]]
        result = inverse_2x2(matrix)
        self.assertIsNone(result)

    def test_identity_matrix(self):
        # Test case: Identity matrix
        matrix = [[1, 0], [0, 1]]
        expected = [[1.0, 0.0], [0.0, 1.0]]
        result = inverse_2x2(matrix)
        self.assertEqual(result, expected)

    def test_matrix_with_decimals(self):
        # Test case: Matrix with decimal numbers
        matrix = [[1.5, 2.5], [3.5, 4.5]]
        expected = [[-2.25, 1.25], [1.75, -0.75]]
        result = inverse_2x2(matrix)
        self.assertEqual(result, expected)

    def test_matrix_with_negative_numbers(self):
        # Test case: Matrix with negative numbers
        matrix = [[-1, -2], [-3, -4]]
        expected = [[2.0, -1.0], [-1.5, 0.5]]
        result = inverse_2x2(matrix)
        self.assertEqual(result, expected)

if __name__ == '__main__':
    unittest.main() 