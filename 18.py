import numpy as np

def k_fold_cross_validation(X: np.n<PERSON><PERSON>, y: np.n<PERSON><PERSON>, k=5, shuffle=True):
    """
    generate train and test splits for K-Fold Cross-Validation. Your task is to divide the dataset into k folds and return a list of train-test indices for each fold.
    Example:
    Input:

    k_fold_cross_validation(np.array([0,1,2,3,4,5,6,7,8,9]), np.array([0,1,2,3,4,5,6,7,8,9]), k=5, shuffle=False)

    Output:

    [([2, 3, 4, 5, 6, 7, 8, 9], [0, 1]), ([0, 1, 4, 5, 6, 7, 8, 9], [2, 3]), ([0, 1, 2, 3, 6, 7, 8, 9], [4, 5]), ([0, 1, 2, 3, 4, 5, 8, 9], [6, 7]), ([0, 1, 2, 3, 4, 5, 6, 7], [8, 9])]

    Reasoning:

    The function splits the dataset into 5 folds without shuffling and returns train-test splits for each iteration.
    """
    n_samples = len(X)
    indices = np.arange(n_samples)
    
    if shuffle:
        np.random.shuffle(indices)
    
    fold_size = n_samples // k
    remainder = n_samples % k
    
    splits = []
    start_idx = 0
    
    for i in range(k):
        # Calculate the size of this fold
        current_fold_size = fold_size + (1 if i < remainder else 0)
        
        # Get test indices for this fold
        test_indices = indices[start_idx:start_idx + current_fold_size]
        
        # Get train indices (all indices except test indices)
        train_indices = np.concatenate([indices[:start_idx], indices[start_idx + current_fold_size:]])
        
        splits.append((train_indices.tolist(), test_indices.tolist()))
        start_idx += current_fold_size
    
    return splits
    