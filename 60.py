import numpy as np
import math

def compute_tf_idf(corpus, query):
	"""
	Compute TF-IDF scores for a query against a corpus of documents.
    
	:param corpus: List of documents, where each document is a list of words
	:param query: List of words in the query
	:return: List of lists containing TF-IDF scores for the query words in each document
	
    implement a function that computes the TF-IDF scores for a query against a given corpus of documents.
    
    Function Signature

    Write a function compute_tf_idf(corpus, query) that takes the following inputs:

        corpus: A list of documents, where each document is a list of words.
        query: A list of words for which you want to compute the TF-IDF scores.

    Output

    The function should return a list of lists containing the TF-IDF scores for the query words in each document, rounded to five decimal places.
    Important Considerations

        Handling Division by Zero:
        When implementing the Inverse Document Frequency (IDF) calculation, you must account for cases where a term does not appear in any document (df = 0). This can lead to division by zero in the standard IDF formula. Add smoothing (e.g., adding 1 to both numerator and denominator) to avoid such errors.

        Empty Corpus:
        Ensure your implementation gracefully handles the case of an empty corpus. If no documents are provided, your function should either raise an appropriate error or return an empty result. This will ensure the program remains robust and predictable.

        Edge Cases:
            Query terms not present in the corpus.
            Documents with no words.
            Extremely large or small values for term frequencies or document frequencies.

    By addressing these considerations, your implementation will be robust and handle real-world scenarios effectively.
    Example:
    Input:

    corpus = [
        ["the", "cat", "sat", "on", "the", "mat"],
        ["the", "dog", "chased", "the", "cat"],
        ["the", "bird", "flew", "over", "the", "mat"]
    ]
    query = ["cat"]

    print(compute_tf_idf(corpus, query))

    Output:

    [[0.21461], [0.25754], [0.0]]

    Reasoning:

    The TF-IDF scores for the word "cat" in each document are computed and rounded to five decimal places.

    """
	if not corpus:
		return []

	if not query:
		return [[] for _ in corpus]

	num_docs = len(corpus)
	
	# Calculate IDF for each unique term in the query
	idf_scores = {}
	unique_query_terms = set(query)
	for term in unique_query_terms:
		# Calculate document frequency (df)
		doc_freq = sum(1 for doc in corpus if term in doc)
		# Calculate IDF with smoothing (adding 1 to numerator and denominator)
		# and adding 1 to the result to prevent negative values.
		idf = math.log((num_docs + 1) / (doc_freq + 1)) + 1
		idf_scores[term] = idf

	# Calculate TF-IDF scores for each document
	tf_idf_scores = []
	for doc in corpus:
		doc_scores = []
		doc_len = len(doc)
		
		# Iterate through the original query to maintain order and handle duplicates
		for term in query:
			# Calculate Term Frequency (TF)
			if doc_len == 0:
				tf = 0.0
			else:
				term_count = doc.count(term)
				tf = term_count / doc_len
			
			# Get the pre-calculated IDF score
			idf = idf_scores[term]
			
			# Calculate TF-IDF
			tf_idf = tf * idf
			doc_scores.append(round(tf_idf, 5))
			
		tf_idf_scores.append(doc_scores)

	return tf_idf_scores

	
