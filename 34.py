import numpy as np

def to_categorical(x, n_col=None):
	"""Write a Python function to perform one-hot encoding of nominal values. The function should take in a 1D numpy array x of integer values and an optional integer n_col representing the number of columns for the one-hot encoded array. If n_col is not provided, it should be automatically determined from the input array.
	Each element in the input array is transformed into a one-hot encoded vector, where the index corresponding to the value in the input array is set to 1, and all other indices are set to 0.
	"""
	# Convert input to numpy array if it's not already
	x = np.array(x)
	
	# If n_col is not provided, determine it from the input array
	if n_col is None:
		n_col = np.max(x) + 1
	
	# Create the one-hot encoded array
	result = np.zeros((len(x), n_col))
	
	# Set the appropriate indices to 1
	for i, val in enumerate(x):
		if val < n_col:  # Ensure the value is within bounds
			result[i, val] = 1
	
	return result
	