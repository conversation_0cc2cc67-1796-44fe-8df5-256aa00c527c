import numpy as np
def linear_regression_normal_equation(X: list[list[float]], y: list[float]) -> list[float]:
	"""performs linear regression using the normal equation. 
	The function should take a matrix X (features) and a vector y (target) as input, 
	and return the coefficients of the linear regression model. 
	Round your answer to four decimal places, -0.0 is a valid result for rounding a very small number
	Example:
    Input:

    X = [[1, 1], [1, 2], [1, 3]], y = [1, 2, 3]

    Output:

    [0.0, 1.0]

    Reasoning:

    The linear model is y = 0.0 + 1.0*x, perfectly fitting the input data.
	"""
	# Convert inputs to numpy arrays
	X = np.array(X)
	y = np.array(y)
	
	# Calculate theta using normal equation: theta = (X^T * X)^(-1) * X^T * y
	theta = np.linalg.inv(X.T @ X) @ X.T @ y
	
	# Round to 4 decimal places
	theta = np.round(theta, 4)
	
	# Convert back to list and handle -0.0 case
	return [float(x) for x in theta]