import numpy as np

def gauss_seidel(A, b, n, x_ini=None):
    """implement the Gauss-<PERSON><PERSON><PERSON> method, an iterative technique for solving a system of linear equations (Ax = b).

    The function should iteratively update the solution vector (x) by using the most recent values available during the iteration process.

    Write a function gauss_seidel(A, b, n, x_ini=None) where:

        A is a square matrix of coefficients,
        b is the right-hand side vector,
        n is the number of iterations,
        x_ini is an optional initial guess for (x) (if not provided, assume a vector of zeros).

    The function should return the approximated solution vector (x) after performing the specified number of iterations.
    Example:
    Input:

    A = np.array([[4, 1, 2], [3, 5, 1], [1, 1, 3]], dtype=float)
    b = np.array([4, 7, 3], dtype=float)

    n = 100
    print(gauss_seidel(A, b, n))

    Output:

    # [0.2, 1.4, 0.8]  (Approximate, values may vary depending on iterations)

    Reasoning:

    The Gauss-Se<PERSON><PERSON> method iteratively updates the solution vector (x) until convergence. The output is an approximate solution to the linear system.
    """
    if x_ini is None:
        x = np.zeros_like(b, dtype=float)
    else:
        x = np.array(x_ini, dtype=float)

    for _ in range(n):
        for i in range(A.shape[0]):
            sigma = 0
            for j in range(A.shape[1]):
                if i != j:
                    sigma += A[i, j] * x[j]
            x[i] = (b[i] - sigma) / A[i, i]
    return x
