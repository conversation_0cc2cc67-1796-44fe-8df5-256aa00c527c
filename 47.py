import numpy as np

def gradient_descent(X, y, weights, learning_rate, n_iterations, batch_size=1, method='batch'):
    """implement a single function that can perform three variants of gradient descent Stochastic Gradient Descent (SGD), Batch Gradient Descent, and Mini Batch Gradient Descent using Mean Squared Error (MSE) as the loss function. The function will take an additional parameter to specify which variant to use. Note: Do not shuffle the data

    Example:
    Input:

    import numpy as np

    # Sample data
    X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]])
    y = np.array([2, 3, 4, 5])

    # Parameters
    learning_rate = 0.01
    n_iterations = 1000
    batch_size = 2

    # Initialize weights
    weights = np.zeros(X.shape[1])

    # Test Batch Gradient Descent
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='batch')
    # Test Stochastic Gradient Descent
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic')
    # Test Mini-Batch Gradient Descent
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, batch_size, method='mini_batch')

    Output:

    [float,float]
    [float, float]
    [float, float]

    Reasoning:

    The function should return the final weights after performing the specified variant of gradient descent.
    """
    # Make a copy of weights to avoid modifying the original
    weights = weights.copy()
    m = len(y)

    if method == 'batch':
        # Batch Gradient Descent: θ = θ - α * (2/m) * Σ(h_θ(x^(i)) - y^(i)) * x^(i)
        # n_iterations represents the number of epochs (full dataset passes)
        for epoch in range(n_iterations):
            predictions = np.dot(X, weights)
            errors = predictions - y
            gradients = (2/m) * np.dot(X.T, errors)
            weights -= learning_rate * gradients

    elif method == 'stochastic':
        # Stochastic Gradient Descent: θ = θ - α * 2 * (h_θ(x^(i)) - y^(i)) * x^(i)
        # n_iterations represents the number of epochs
        for epoch in range(n_iterations):
            for i in range(m):
                X_i = X[i]
                y_i = y[i]

                prediction = np.dot(X_i, weights)
                error = prediction - y_i
                gradient = 2 * error * X_i
                weights -= learning_rate * gradient

    elif method == 'mini_batch':
        # Mini-Batch Gradient Descent: θ = θ - α * (2/b) * Σ(h_θ(x^(i)) - y^(i)) * x^(i)
        # n_iterations represents the number of epochs
        for epoch in range(n_iterations):
            for start_idx in range(0, m, batch_size):
                end_idx = min(start_idx + batch_size, m)
                X_batch = X[start_idx:end_idx]
                y_batch = y[start_idx:end_idx]

                predictions = np.dot(X_batch, weights)
                errors = predictions - y_batch
                gradients = (2/len(y_batch)) * np.dot(X_batch.T, errors)
                weights -= learning_rate * gradients

    else:
        raise ValueError("Invalid method. Choose from 'batch', 'stochastic', or 'mini_batch'.")

    return weights