import math

def softmax(scores: list[float]) -> list[float]:
	"""computes the softmax activation for a given list of scores. 
	The function should return the softmax values as a list, 
	each rounded to four decimal places.
	
	The softmax function converts a list of values into a probability distribution. 
	The probabilities are proportional to the exponential of each element divided by the sum of the exponentials of all elements in the list.
	"""
	exponentials = [math.exp(s) for s in scores]
	sum_of_exponentials = sum(exponentials)
	probabilities = [round(e / sum_of_exponentials, 4) for e in exponentials]
	return probabilities