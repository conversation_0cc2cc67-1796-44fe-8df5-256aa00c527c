import numpy as np
from itertools import combinations_with_replacement

def polynomial_features(X, degree):
    """Write a Python function that takes a 2-D NumPy array X and an integer degree, generates all polynomial feature combinations of the columns of X up to the given degree inclusive, then sorts the resulting features for each sample from lowest to highest value. The function should return a new 2-D NumPy array whose rows correspond to the input samples and whose columns are the ascending-sorted polynomial features.
    
    Example:
    Input:

    X = np.array([[2, 3],
                  [3, 4],
                  [5, 6]])
    degree = 2
    output = polynomial_features(X, degree)
    print(output)

    Output:

    [[ 1.  2.  3.  4.  6.  9.]
     [ 1.  3.  4.  9. 12. 16.]
     [ 1.  5.  6. 25. 30. 36.]]

    Reasoning:

    For degree = 2, the raw polynomial terms for the first sample are [1, 2, 3, 4, 6, 9]. Sorting them from smallest to largest yields [1, 2, 3, 4, 6, 9]. The same procedure is applied to every sample.
    """
    n_samples, n_features = X.shape
    result = []
    
    for sample in X:
        # Generate all polynomial combinations up to the given degree
        polynomial_terms = []
        
        # Add constant term (degree 0)
        polynomial_terms.append(1.0)
        
        # Generate terms for each degree from 1 to the given degree
        for d in range(1, degree + 1):
            # Get all combinations with replacement of features for current degree
            for combo in combinations_with_replacement(range(n_features), d):
                # Calculate the product of the selected features
                term = 1.0
                for feature_idx in combo:
                    term *= sample[feature_idx]
                polynomial_terms.append(term)
        
        # Sort the polynomial terms in ascending order
        polynomial_terms.sort()
        result.append(polynomial_terms)
    
    return np.array(result)

# Test the function with the provided example
if __name__ == "__main__":
    X = np.array([[2, 3],
                  [3, 4],
                  [5, 6]])
    degree = 2
    output = polynomial_features(X, degree)
    print("Input X:")
    print(X)
    print(f"\nDegree: {degree}")
    print("\nOutput:")
    print(output)
