import numpy as np

def get_random_subsets(X, y, n_subsets, replacements=True, seed=42):
	"""Write a Python function to generate random subsets of a given dataset. The function should take in a 2D numpy array X, a 1D numpy array y, an integer n_subsets, and a boolean replacements. It should return a list of n_subsets random subsets of the dataset, where each subset is a tuple of (X_subset, y_subset). If replacements is True, the subsets should be created with replacements; otherwise, without replacements.
    
	Example:
    Input:

    X = np.array([[1, 2],
                      [3, 4],
                      [5, 6],
                      [7, 8],
                      [9, 10]])
        y = np.array([1, 2, 3, 4, 5])
        n_subsets = 3
        replacements = False
        get_random_subsets(X, y, n_subsets, replacements)

    Output:

    [array([[7, 8],
                [1, 2]]), 
         array([4, 1])]
         
        [array([[9, 10],
                [5, 6]]), 
         array([5, 3])]
         
        [array([[3, 4],
                [5, 6]]), 
         array([2, 3])]

    Reasoning:

    The function generates three random subsets of the dataset without replacements. Each subset includes 50% of the samples (since replacements=False). The samples are randomly selected without duplication.
    """
	# Set random seed for reproducibility
	np.random.seed(seed)
	
	# Get the number of samples
	n_samples = X.shape[0]
	
	# Determine subset size (50% of samples when replacements=False, otherwise use n_samples)
	if not replacements:
		subset_size = n_samples // 2
	else:
		# For replacements=True, use n_samples as the subset size
		subset_size = n_samples
	
	subsets = []
	
	for _ in range(n_subsets):
		if replacements:
			# With replacements: randomly select indices with replacement
			indices = np.random.choice(n_samples, size=subset_size, replace=True)
		else:
			# Without replacements: randomly select indices without replacement
			indices = np.random.choice(n_samples, size=subset_size, replace=False)
		
		# Create subset of X and y using the selected indices
		X_subset = X[indices]
		y_subset = y[indices]
		
		# Add the subset as a tuple to the list
		subsets.append((X_subset, y_subset))
	
	return subsets

# Example usage
if __name__ == "__main__":
	X = np.array([[1, 2],
				  [3, 4],
				  [5, 6],
				  [7, 8],
				  [9, 10]])
	y = np.array([1, 2, 3, 4, 5])
	n_subsets = 3
	replacements = False
	
	result = get_random_subsets(X, y, n_subsets, replacements)
	
	print("Generated subsets:")
	for i, (X_subset, y_subset) in enumerate(result):
		print(f"Subset {i+1}:")
		print(f"X: {X_subset}")
		print(f"y: {y_subset}")
		print()
	
	# Test case
	print("Test Case:")
	X_test = np.array([[1, 1], [2, 2], [3, 3], [4, 4]])
	y_test = np.array([10, 20, 30, 40])
	result_test = get_random_subsets(X_test, y_test, 1, True, seed=42)
	print(f"Expected: [([[3, 3], [4, 4], [1, 1], [3, 3]], [30, 40, 10, 30])]")
	print(f"Actual: {result_test}")
	