def calculate_covariance_matrix(vectors: list[list[float]]) -> list[list[float]]:
	"""calculate the covariance matrix for a given set of vectors. 
	The function should take a list of lists, 
	where each inner list represents a feature with its observations, 
	and return a covariance matrix as a list of lists. 
	"""
	if not vectors or not vectors[0]:
		raise ValueError("Input vectors cannot be empty")
	
	n_features = len(vectors)
	n_samples = len(vectors[0])
	
	# Calculate the mean of each feature
	means = [sum(feature) / n_samples for feature in vectors]
	
	# Calculate the covariance matrix
	covariance_matrix = [[0.0 for _ in range(n_features)] for _ in range(n_features)]
	
	for i in range(n_features):
		for j in range(n_features):
			for k in range(n_samples):
				covariance_matrix[i][j] += (vectors[i][k] - means[i]) * (vectors[j][k] - means[j])
			covariance_matrix[i][j] /= (n_samples - 1)
	
	return covariance_matrix

import unittest
import numpy as np

class TestCovarianceMatrix(unittest.TestCase):
	def test_simple_case(self):
		# Test case with simple 2x2 data
		vectors = [
			[1, 2],  # feature 1
			[3, 4]   # feature 2
		]
		result = calculate_covariance_matrix(vectors)
		expected = [[0.5, 0.5], [0.5, 0.5]]
		np.testing.assert_array_almost_equal(result, expected)

	def test_identity_matrix(self):
		# Test case where features are independent
		vectors = [
			[1, 0, 0],  # feature 1
			[0, 1, 0],  # feature 2
			[0, 0, 1]   # feature 3
		]
		result = calculate_covariance_matrix(vectors)
		expected = [[0.333333, -0.166667, -0.166667], 
				   [-0.166667, 0.333333, -0.166667], 
				   [-0.166667, -0.166667, 0.333333]]
		np.testing.assert_array_almost_equal(result, expected)

	def test_constant_features(self):
		# Test case with constant features
		vectors = [
			[1, 1, 1],  # constant feature
			[2, 2, 2]   # constant feature
		]
		result = calculate_covariance_matrix(vectors)
		expected = [[0.0, 0.0], [0.0, 0.0]]
		np.testing.assert_array_almost_equal(result, expected)

	def test_empty_input(self):
		# Test case with empty input
		with self.assertRaises(ValueError):
			calculate_covariance_matrix([])

if __name__ == '__main__':
	unittest.main()
