import numpy as np

def rref(matrix):
    """implement a function that converts a given matrix into its Reduced Row Echelon Form (RREF). The RREF of a matrix is a special form where each leading entry in a row is 1, and all other elements in the column containing the leading 1 are zeros, except for the leading 1 itself.

    However, there are some additional details to keep in mind:

        Diagonal entries can be 0 if the matrix is reducible (i.e., the row corresponding to that position can be eliminated entirely).
        Some rows may consist entirely of zeros.
        If a column contains a pivot (a leading 1), all other entries in that column should be zero.

    Your task is to implement the RREF algorithm, which must handle these cases and convert any given matrix into its RREF.
    Example:
    Input:

    import numpy as np

    matrix = np.array(
        [[1, 2, -1, -4],
         [2, 3, -1, -11],
         [-2, 0, -3, 22]]
    )

    rref_matrix = rref(matrix)
    print(rref_matrix)

    Output:

    # array([
    #    [ 1.  0.  0. -8.],
    #    [ 0.  1.  0.  1.],
    #    [-0. -0.  1. -2.]
    # ])

    Reasoning:

    The given matrix is converted to its Reduced Row Echelon Form (RREF) where each leading entry is 1, and all other entries in the leading columns are zero.
    """
    matrix = matrix.astype(float)  # Ensure float type for division
    rows, cols = matrix.shape
    pivot_row = 0

    for col in range(cols):
        if pivot_row >= rows:
            break

        # Find a row with a non-zero entry in the current column, starting from pivot_row
        i = pivot_row
        while i < rows and np.isclose(matrix[i, col], 0):
            i += 1

        if i == rows:
            # No non-zero entry found in this column from pivot_row downwards
            # Move to the next column, without incrementing pivot_row
            continue

        # Swap current pivot_row with the row containing the pivot
        matrix[[pivot_row, i]] = matrix[[i, pivot_row]]

        # Make the pivot element 1
        lv = matrix[pivot_row, col]
        matrix[pivot_row] = matrix[pivot_row] / lv

        # Eliminate other non-zero elements in the current pivot column
        for j in range(rows):
            if j != pivot_row:
                # Subtract multiples of the pivot row from other rows
                matrix[j] = matrix[j] - matrix[j, col] * matrix[pivot_row]
        
        pivot_row += 1

    return matrix