import numpy as np
import math

def svd_2x2(A: np.n<PERSON><PERSON>) -> tuple:
    """Given a 2x2 matrix, write a Python function to compute its Singular Value Decomposition (SVD). The function should return the matrices U, S, and V such that A = U * S * V, use the method described in this post https://metamerist.blogspot.com/2006/10/linear-algebra-for-graphics-geeks-svd.html
    
    Example:
    Input:

    A = [[-10, 8], 
         [10, -1]]

    Output:

    (array([[  0.8, -0.6], [-0.6, -0.8]], 
         array([15.65247584,  4.47213595]), 
         array([[ -0.89442719,  0.4472136], [ -0.4472136 , -0.89442719]]))

    Reasoning:
    
    The SVD of the matrix A is calculated using the eigenvalues and eigenvectors of A^T A and A A^T. The singular values are the square roots of the eigenvalues, and the eigenvectors form the columns of matrices U and V.
    """
    # Ensure A is 2x2
    if A.shape != (2, 2):
        raise ValueError("Matrix A must be 2x2")
    
    # Extract matrix elements
    a, b = A[0, 0], A[0, 1]
    c, d = A[1, 0], A[1, 1]
    
    # Compute A^T * A
    ATA = A.T @ A
    
    # Compute A * A^T  
    AAT = A @ A.T
    
    # Calculate eigenvalues using the quadratic formula method from the blog
    # For 2x2 matrix, the characteristic equation is: λ² - (a+d)λ + (ad-bc) = 0
    # where a, d are diagonal elements and b, c are off-diagonal elements
    
    # For A^T * A matrix
    ata_a, ata_b = ATA[0, 0], ATA[0, 1]
    ata_c, ata_d = ATA[1, 0], ATA[1, 1]
    
    # Characteristic equation coefficients: λ² - (a+d)λ + (ad-bc) = 0
    # So: a=1, b=-(a+d), c=(ad-bc)
    coeff_a = 1
    coeff_b = -(ata_a + ata_d)
    coeff_c = ata_a * ata_d - ata_b * ata_c
    
    # Use quadratic formula: λ = (-b ± √(b² - 4ac)) / (2a)
    discriminant = coeff_b**2 - 4 * coeff_a * coeff_c
    if discriminant < 0:
        # Handle complex eigenvalues (shouldn't happen for real matrices)
        discriminant = 0
    
    sqrt_discriminant = math.sqrt(discriminant)
    lambda1 = (-coeff_b + sqrt_discriminant) / (2 * coeff_a)
    lambda2 = (-coeff_b - sqrt_discriminant) / (2 * coeff_a)
    
    # Sort eigenvalues in descending order
    eigenvals = sorted([lambda1, lambda2], reverse=True)
    
    # Singular values are square roots of eigenvalues
    singular_values = np.array([math.sqrt(abs(eigenval)) for eigenval in eigenvals])
    
    # Calculate eigenvectors for A * A^T (for U matrix)
    U = np.zeros((2, 2))
    
    for i, eigenval in enumerate(eigenvals):
        # Solve (AAT - λI)x = 0 for each eigenvalue
        # This gives us the system of equations:
        # (AAT[0,0] - λ)x₁ + AAT[0,1]x₂ = 0
        # AAT[1,0]x₁ + (AAT[1,1] - λ)x₂ = 0
        
        # For the first equation: (AAT[0,0] - λ)x₁ + AAT[0,1]x₂ = 0
        # We can express x₁ in terms of x₂: x₁ = -AAT[0,1]/(AAT[0,0] - λ) * x₂
        
        # Check which equation to use based on numerical stability
        det1 = abs(AAT[0, 0] - eigenval)
        det2 = abs(AAT[1, 1] - eigenval)
        
        if det1 > det2 and det1 > 1e-10:
            # Use first equation: (AAT[0,0] - λ)x₁ + AAT[0,1]x₂ = 0
            x2 = 1.0
            x1 = -AAT[0, 1] / (AAT[0, 0] - eigenval) * x2
        elif det2 > 1e-10:
            # Use second equation: AAT[1,0]x₁ + (AAT[1,1] - λ)x₂ = 0
            x1 = 1.0
            x2 = -AAT[1, 0] / (AAT[1, 1] - eigenval) * x1
        else:
            # Both equations are degenerate, use standard basis
            if i == 0:
                x1, x2 = 1.0, 0.0
            else:
                x1, x2 = 0.0, 1.0
        
        # Normalize the eigenvector
        norm = math.sqrt(x1**2 + x2**2)
        if norm > 1e-10:
            U[0, i] = x1 / norm
            U[1, i] = x2 / norm
        else:
            # Fallback to standard basis
            if i == 0:
                U[0, i], U[1, i] = 1.0, 0.0
            else:
                U[0, i], U[1, i] = 0.0, 1.0
    
    # Calculate eigenvectors for A^T * A (for V matrix)
    V = np.zeros((2, 2))
    
    for i, eigenval in enumerate(eigenvals):
        # Solve (ATA - λI)x = 0 for each eigenvalue
        # Check which equation to use based on numerical stability
        det1 = abs(ATA[0, 0] - eigenval)
        det2 = abs(ATA[1, 1] - eigenval)
        
        if det1 > det2 and det1 > 1e-10:
            # Use first equation: (ATA[0,0] - λ)x₁ + ATA[0,1]x₂ = 0
            x2 = 1.0
            x1 = -ATA[0, 1] / (ATA[0, 0] - eigenval) * x2
        elif det2 > 1e-10:
            # Use second equation: ATA[1,0]x₁ + (ATA[1,1] - λ)x₂ = 0
            x1 = 1.0
            x2 = -ATA[1, 0] / (ATA[1, 1] - eigenval) * x1
        else:
            # Both equations are degenerate, use standard basis
            if i == 0:
                x1, x2 = 1.0, 0.0
            else:
                x1, x2 = 0.0, 1.0
        
        # Normalize the eigenvector
        norm = math.sqrt(x1**2 + x2**2)
        if norm > 1e-10:
            V[0, i] = x1 / norm
            V[1, i] = x2 / norm
        else:
            # Fallback to standard basis
            if i == 0:
                V[0, i], V[1, i] = 1.0, 0.0
            else:
                V[0, i], V[1, i] = 0.0, 1.0
    
    # Ensure proper signs for U and V
    # For each column, if the corresponding singular value is non-zero,
    # we can compute the correct sign by checking A * v_i = σ_i * u_i
    for i in range(2):
        if singular_values[i] > 1e-10:  # Check if singular value is non-zero
            # Compute A * v_i
            Av = A @ V[:, i]
            # Compute σ_i * u_i
            sigma_u = singular_values[i] * U[:, i]
            
            # Check if signs need to be flipped
            if np.dot(Av, sigma_u) < 0:
                U[:, i] = -U[:, i]
    
    return U, singular_values, V.T

# Test the implementation
if __name__ == "__main__":
    # Test with the example from the docstring
    A = np.array([[-10, 8], [10, -1]])
    print("Test 1 - Example from docstring:")
    print("Input matrix A:")
    print(A)
    print()
    
    U, S, V = svd_2x2(A)
    print("SVD decomposition:")
    print("U =")
    print(U)
    print("S =", S)
    print("V =")
    print(V)
    print()
    
    # Verify the decomposition: A = U * S * V
    S_diag = np.diag(S)
    reconstructed = U @ S_diag @ V
    print("Reconstructed A = U * S * V:")
    print(reconstructed)
    print()
    print("Error (should be close to zero):")
    print(np.linalg.norm(A - reconstructed))
    print("\n" + "="*50 + "\n")
    
    # Test with identity matrix
    A2 = np.array([[1, 0], [0, 1]])
    print("Test 2 - Identity matrix:")
    print("Input matrix A:")
    print(A2)
    print()
    
    U2, S2, V2 = svd_2x2(A2)
    print("SVD decomposition:")
    print("U =")
    print(U2)
    print("S =", S2)
    print("V =")
    print(V2)
    print()
    
    reconstructed2 = U2 @ np.diag(S2) @ V2
    print("Error (should be close to zero):")
    print(np.linalg.norm(A2 - reconstructed2))
    print("\n" + "="*50 + "\n")
    
    # Test with a random matrix
    np.random.seed(42)
    A3 = np.random.randn(2, 2)
    print("Test 3 - Random matrix:")
    print("Input matrix A:")
    print(A3)
    print()
    
    U3, S3, V3 = svd_2x2(A3)
    print("SVD decomposition:")
    print("U =")
    print(U3)
    print("S =", S3)
    print("V =")
    print(V3)
    print()
    
    reconstructed3 = U3 @ np.diag(S3) @ V3
    print("Error (should be close to zero):")
    print(np.linalg.norm(A3 - reconstructed3))
