def matrixmul(a:list[list[int|float]],
              b:list[list[int|float]])-> list[list[int|float]]:
	"""multiply two matrices together (return -1 if shapes of matrix dont aline), i.e. C=A⋅B"""
	# Check if the number of columns in A is equal to the number of rows in B
	if len(a[0]) != len(b):
		return -1
	
	# Initialize the result matrix with zeros
	c = [[0 for _ in range(len(b[0]))] for _ in range(len(a))]
	
	# Perform matrix multiplication
	for i in range(len(a)):
		for j in range(len(b[0])):
			for k in range(len(b)):
				c[i][j] += a[i][k] * b[k][j]
	
	return c