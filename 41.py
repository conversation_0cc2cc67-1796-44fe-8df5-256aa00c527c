import numpy as np

def simple_conv2d(input_matrix: np.n<PERSON><PERSON>, kernel: np.ndar<PERSON>, padding: int, stride: int):
	"""implement a 2D convolutional layer in Python. This function will process an input matrix using a specified convolutional kernel, padding, and stride.
    
	Example:
    Input:

    import numpy as np

    input_matrix = np.array([
        [1, 2, 3, 4],
        [5, 6, 7, 8],
        [9, 10, 11, 12],
        [13, 14, 15, 16]
    ])

    kernel = np.array([
        [1, 0],
        [-1, 1]
    ])

    padding = 1
    stride = 2

    output = simple_conv2d(input_matrix, kernel, padding, stride)
    print(output)

    Output:

    [[ 1.  1. -4.],[ 9.  7. -4.],[ 0. 14. 16.]]

    Reasoning:

    The function performs a 2D convolution operation on the input matrix using the specified kernel, padding, and stride. The output matrix contains the results of the convolution operation
    """
	input_height, input_width = input_matrix.shape
	kernel_height, kernel_width = kernel.shape

	# Apply padding to the input matrix
	padded_input = np.pad(input_matrix, padding, mode='constant', constant_values=0)
	padded_height, padded_width = padded_input.shape

	# Calculate output dimensions
	output_height = (padded_height - kernel_height) // stride + 1
	output_width = (padded_width - kernel_width) // stride + 1

	# Initialize output matrix
	output_matrix = np.zeros((output_height, output_width))

	# Perform convolution
	for i in range(output_height):
		for j in range(output_width):
			# Calculate the starting position in the padded input
			start_i = i * stride
			start_j = j * stride

			# Extract the region of interest
			region = padded_input[start_i:start_i + kernel_height, start_j:start_j + kernel_width]

			# Perform element-wise multiplication and sum (convolution operation)
			output_matrix[i, j] = np.sum(region * kernel)

	return output_matrix
