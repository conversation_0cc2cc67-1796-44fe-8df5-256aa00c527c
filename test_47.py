import numpy as np
from importlib import import_module

# Import the gradient_descent function
module = import_module('47')
gradient_descent = module.gradient_descent

# Sample data
X = np.array([[1, 1], [2, 1], [3, 1], [4, 1]], dtype=np.float64)
y = np.array([2, 3, 4, 5], dtype=np.float64)

# Parameters
learning_rate = 0.01
n_iterations = 1000
batch_size = 2

# Initialize weights
initial_weights = np.zeros(X.shape[1], dtype=np.float64)

def test_batch_gradient_descent():
    weights = initial_weights.copy()
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='batch')
    predictions = np.dot(X, final_weights)
    # Assert that the predictions are close to the actual y values
    np.testing.assert_allclose(predictions, y, rtol=1e-2, atol=1e-2)

def test_stochastic_gradient_descent():
    weights = initial_weights.copy()
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, method='stochastic')
    predictions = np.dot(X, final_weights)
    np.testing.assert_allclose(predictions, y, rtol=1e-2, atol=1e-2)

def test_mini_batch_gradient_descent():
    weights = initial_weights.copy()
    final_weights = gradient_descent(X, y, weights, learning_rate, n_iterations, batch_size, method='mini_batch')
    predictions = np.dot(X, final_weights)
    np.testing.assert_allclose(predictions, y, rtol=1e-2, atol=1e-2)