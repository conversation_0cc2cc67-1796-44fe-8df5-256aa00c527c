import numpy as np

def compute_qkv(X, W_q, W_k, W_v):
    Q = np.matmul(X, W_q)
    K = np.matmul(X, W_k)
    V = np.matmul(X, W_v)
    return Q, K, V

def self_attention(Q, K, V):
    """ implement the self-attention mechanism, which is a fundamental component of transformer models, widely used in natural language processing and computer vision tasks. The self-attention mechanism allows a model to dynamically focus on different parts of the input sequence when generating a contextualized representation.

    Your function should return the self-attention output as a numpy array.
    Example:
    Input:

    import numpy as np

    X = np.array([[1, 0], [0, 1]])
    W_q = np.array([[1, 0], [0, 1]])
    W_k = np.array([[1, 0], [0, 1]])
    W_v = np.array([[1, 2], [3, 4]])

    Q, K, V = compute_qkv(X, W_q, W_k, W_v)
    output = self_attention(Q, K, V)

    print(output)

    Output:

    # [[1.660477 2.660477]
    #  [2.339523 3.339523]]

    Reasoning:

    The self-attention mechanism calculates the attention scores for each input, determining how much focus to put on other inputs when generating a contextualized representation. The output is the weighted sum of the values based on the attention scores.
    Learn About topic
    """
	# Calculate attention scores
    # Q: (batch_size, seq_len, d_k)
    # K: (batch_size, seq_len, d_k)
    # K.T: (batch_size, d_k, seq_len)
    # scores: (batch_size, seq_len, seq_len)
    scores = np.matmul(Q, K.T)

    # Scale scores
    d_k = Q.shape[-1]
    scaled_scores = scores / np.sqrt(d_k)

    # Apply softmax to get attention weights
    # Subtract max for numerical stability
    exp_scores = np.exp(scaled_scores - np.max(scaled_scores, axis=-1, keepdims=True))
    attention_weights = exp_scores / np.sum(exp_scores, axis=-1, keepdims=True)

    # Multiply weights by V
    # attention_weights: (batch_size, seq_len, seq_len)
    # V: (batch_size, seq_len, d_v)
    # output: (batch_size, seq_len, d_v)
    attention_output = np.matmul(attention_weights, V)

    return attention_output
