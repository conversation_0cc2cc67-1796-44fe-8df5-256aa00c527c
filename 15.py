import numpy as np
def linear_regression_gradient_descent(X: np.n<PERSON><PERSON>, y: np.n<PERSON><PERSON>, alpha: float, iterations: int) -> np.ndarray:
	"""performs linear regression using gradient descent. 
	The function should take NumPy arrays X (features with a column of ones for the intercept) and y (target) as input, 
	along with learning rate alpha and the number of iterations, and return the coefficients of the linear regression model as a NumPy array. 
	Round your answer to four decimal places. -0.0 is a valid result for rounding a very small number.
    
	Example:
    Input:

    X = np.array([[1, 1], [1, 2], [1, 3]]), y = np.array([1, 2, 3]), alpha = 0.01, iterations = 1000

    Output:

    np.array([0.1107, 0.9513])

    Reasoning:

    The linear model is y = 0.0 + 1.0*x, which fits the input data after gradient descent optimization.
    """
	m, n = X.shape
	theta = np.zeros(n)
	
	for _ in range(iterations):
		# Calculate predictions
		predictions = X @ theta
		
		# Calculate error
		error = predictions - y
		
		# Calculate gradient
		gradient = (X.T @ error) / m
		
		# Update theta
		theta = theta - alpha * gradient
	
	# Round to 4 decimal places
	return np.round(theta, 4)