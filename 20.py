import math

def learn_decision_tree(examples: list[dict], attributes: list[str], target_attr: str) -> dict:
	"""implements the decision tree learning algorithm for classification. 
	The function should use recursive binary splitting based on entropy and information gain to build a decision tree.
	It should take a list of examples (each example is a dict of attribute-value pairs) and a list of attribute names as input, 
	and return a nested dictionary representing the decision tree.
	
    Example:
    Input:

    examples = [
                        {'Outlook': 'Sunny', 'Temperature': 'Hot', 'Humidity': 'High', 'Wind': 'Weak', 'PlayTennis': 'No'},
                        {'Outlook': 'Sunny', 'Temperature': 'Hot', 'Humidity': 'High', 'Wind': 'Strong', 'PlayTennis': 'No'},
                        {'Outlook': 'Overcast', 'Temperature': 'Hot', 'Humidity': 'High', 'Wind': 'Weak', 'PlayTennis': 'Yes'},
                        {'Outlook': 'Rain', 'Temperature': 'Mild', 'Humidity': 'High', 'Wind': 'Weak', 'PlayT<PERSON>nis': 'Yes'}
                    ],
                    attributes = ['Outlook', 'Temperature', '<PERSON>midity', 'Wind']

    Output:

    {
                'Outlook': {
                    'Sunny': {'Humidity': {'High': 'No', 'Normal': 'Yes'}},
                    'Overcast': 'Yes',
                    'Rain': {'Wind': {'Weak': 'Yes', 'Strong': 'No'}}
                }
            }

    Reasoning:

    Using the given examples, the decision tree algorithm determines that 'Outlook' is the best attribute to split the data initially. 
	When 'Outlook' is 'Overcast', the outcome is always 'Yes', so it becomes a leaf node. In cases of 'Sunny' and 'Rain', 
	it further splits based on 'Humidity' and 'Wind', respectively. 
	The resulting tree structure is able to classify the training examples with the attributes 'Outlook', 'Temperature', 'Humidity', and 'Wind'.
	"""
	def get_entropy(examples: list[dict], target_attr: str) -> float:
		"""Calculate the entropy of the target attribute in the examples."""
		if not examples:
			return 0
		
		# Count the frequency of each target value
		value_counts = {}
		for example in examples:
			value = example[target_attr]
			value_counts[value] = value_counts.get(value, 0) + 1
		
		# Calculate entropy
		entropy = 0
		total = len(examples)
		for count in value_counts.values():
			probability = count / total
			entropy -= probability * math.log2(probability)
		
		return entropy

	def get_information_gain(examples: list[dict], attribute: str, target_attr: str) -> float:
		"""Calculate the information gain of splitting on the given attribute."""
		# Calculate the entropy before splitting
		entropy_before = get_entropy(examples, target_attr)
		
		# Calculate the entropy after splitting
		entropy_after = 0
		attribute_values = {}
		
		# Group examples by attribute value
		for example in examples:
			value = example[attribute]
			if value not in attribute_values:
				attribute_values[value] = []
			attribute_values[value].append(example)
		
		# Calculate weighted entropy for each split
		total = len(examples)
		for value_examples in attribute_values.values():
			weight = len(value_examples) / total
			entropy_after += weight * get_entropy(value_examples, target_attr)
		
		return entropy_before - entropy_after

	def get_most_common_value(examples: list[dict], target_attr: str) -> str:
		"""Get the most common value of the target attribute in the examples."""
		value_counts = {}
		for example in examples:
			value = example[target_attr]
			value_counts[value] = value_counts.get(value, 0) + 1
		return max(value_counts.items(), key=lambda x: x[1])[0]

	def all_same_class(examples: list[dict], target_attr: str) -> bool:
		"""Check if all examples have the same target value."""
		if not examples:
			return True
		first_value = examples[0][target_attr]
		return all(example[target_attr] == first_value for example in examples)

	# Base cases
	if not examples:
		return None
	
	if not attributes:
		return get_most_common_value(examples, target_attr)
	
	if all_same_class(examples, target_attr):
		return examples[0][target_attr]

	# Find the best attribute to split on
	best_attribute = max(attributes, key=lambda attr: get_information_gain(examples, attr, target_attr))
	
	# Create the tree
	tree = {best_attribute: {}}
	
	# Get unique values of the best attribute
	attribute_values = set(example[best_attribute] for example in examples)
	
	# Recursively build subtrees
	remaining_attributes = [attr for attr in attributes if attr != best_attribute]
	
	for value in attribute_values:
		# Get examples where best_attribute = value
		subset = [example for example in examples if example[best_attribute] == value]
		
		if not subset:
			# If no examples with this value, use most common value
			tree[best_attribute][value] = get_most_common_value(examples, target_attr)
		else:
			# Recursively build subtree
			subtree = learn_decision_tree(subset, remaining_attributes, target_attr)
			tree[best_attribute][value] = subtree
	
	return tree