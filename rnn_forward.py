import numpy as np

def rnn_forward(input_sequence: list[list[float]], initial_hidden_state: list[float], Wx: list[list[float]], Wh: list[list[float]], b: list[float]) -> list[float]:
	"""implements a simple Recurrent Neural Network (RNN) cell. The function should process a sequence of input vectors and produce the final hidden state. Use the tanh activation function for the hidden state updates. The function should take as inputs the sequence of input vectors, the initial hidden state, the weight matrices for input-to-hidden and hidden-to-hidden connections, and the bias vector. The function should return the final hidden state after processing the entire sequence, rounded to four decimal places.
    
    Example:
    Input:

    input_sequence = [[1.0], [2.0], [3.0]]
        initial_hidden_state = [0.0]
        Wx = [[0.5]]  # Input to hidden weights
        Wh = [[0.8]]  # Hidden to hidden weights
        b = [0.0]     # Bias

    Output:

    final_hidden_state = [0.9993]

    Reasoning:

    The RNN processes each input in the sequence, updating the hidden state at each step using the tanh activation function.
    Learn About topic
    """
	h_t = np.array(initial_hidden_state)
	Wx_np = np.array(Wx)
	Wh_np = np.array(Wh)
	b_np = np.array(b)

	for x_t in input_sequence:
		x_t_np = np.array(x_t)
		h_t = np.tanh(np.dot(Wx_np, x_t_np) + np.dot(Wh_np, h_t) + b_np)
	
	return np.round(h_t, 4).tolist()