import numpy as np
def pca(data: np.ndarray, k: int) -> np.ndarray:
	"""performs Principal Component Analysis (PCA) from scratch. 
	The function should take a 2D NumPy array as input, where each row represents a data sample and each column represents a feature.
	The function should standardize the dataset, compute the covariance matrix, find the eigenvalues and eigenvectors, 
	and return the principal components (the eigenvectors corresponding to the largest eigenvalues). 
	The function should also take an integer k as input, representing the number of principal components to return.
    Example:
    Input:

    data = np.array([[1, 2], [3, 4], [5, 6]]), k = 1

    Output:

    [[0.7071], [0.7071]]

    Reasoning:

    After standardizing the data and computing the covariance matrix, the eigenvalues and eigenvectors are calculated. 
	The largest eigenvalue's corresponding eigenvector is returned as the principal component, rounded to four decimal places.
	"""
	# Standardize the data
	mean = np.mean(data, axis=0)
	std = np.std(data, axis=0)
	standardized_data = (data - mean) / std

	# Compute covariance matrix
	cov_matrix = np.cov(standardized_data.T)

	# Calculate eigenvalues and eigenvectors
	eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)

	# Sort eigenvalues and eigenvectors in descending order
	idx = eigenvalues.argsort()[::-1]
	eigenvalues = eigenvalues[idx]
	eigenvectors = eigenvectors[:, idx]

	# Select top k eigenvectors
	principal_components = eigenvectors[:, :k]
	for j in range(len(principal_components[0])):
		max_i = np.argmax(abs(principal_components[i][j] for i in range(len(principal_components))))
		if principal_components[max_i][j] < 0:
			principal_components[:, j] = -1 * principal_components[:, j]

	return np.round(principal_components, 4)