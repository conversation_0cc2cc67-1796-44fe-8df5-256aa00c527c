import numpy as np

def batch_iterator(X, y=None, batch_size=64):
	"""Implement a batch iterable function that samples in a numpy array X and an optional numpy array y. 
	The function should return batches of a specified size. If y is provided, the function should return batches of (X, y) pairs; 
	otherwise, it should return batches of X only.
	"""
	n = len(X)
	indices = np.arange(n)
	np.random.shuffle(indices)
	
    result = []
	for i in range(0, n, batch_size):
		batch_indices = indices[i:i+batch_size]
		if y is not None:
			result.append( (X[batch_indices].tolist(), y[batch_indices].tolist()) )
		else:
			result.append( X[batch_indices].tolist()    )
	return result