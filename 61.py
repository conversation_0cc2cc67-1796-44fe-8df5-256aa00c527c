import numpy as np

def f_score(y_true, y_pred, beta):
	"""
	Calculate F-Score for a binary classification task.

	:param y_true: Numpy array of true labels
	:param y_pred: Numpy array of predicted labels
	:param beta: The weight of precision in the harmonic mean
	:return: F-Score rounded to three decimal places

    implement a function that calculates the F-Score for a binary classification task. The F-Score combines both Precision and Recall into a single metric, providing a balanced measure of a model's performance.

    Write a function f_score(y_true, y_pred, beta) where:

        y_true: A numpy array of true labels (binary).
        y_pred: A numpy array of predicted labels (binary).
        beta: A float value that adjusts the importance of Precision and Recall. When beta=1, it computes the F1-Score, a balanced measure of both Precision and Recall.

    The function should return the F-Score rounded to three decimal places.
    Example:
    Input:

    y_true = np.array([1, 0, 1, 1, 0, 1])
    y_pred = np.array([1, 0, 1, 0, 0, 1])
    beta = 1

    print(f_score(y_true, y_pred, beta))

    Output:

    0.857

    Reasoning:

    The F-Score for the binary classification task is calculated using the true labels, predicted labels, and beta value.

        F-Score, also called F-measure, is a measure of predictive performance that's calculated from the Precision and Recall metrics.
    Mathematical Definition

    The FβFβ​ score applies additional weights, valuing one of precision or recall more than the other. When ββ equals 1, also known as the F1-Score, it symmetrically represents both precision and recall in one metric. The F-Score can be calculated using the following formula:
    Fβ=(1+β2)×precision×recall(β2×precision)+recall
    Fβ​=(1+β2)×(β2×precision)+recallprecision×recall​

    Where:

        Recall: The number of true positive results divided by the number of all samples that should have been identified as positive.
        Precision: The number of true positive results divided by the number of all samples predicted to be positive, including those not identified correctly.

    Implementation Instructions

    In this problem, you will implement a function to calculate the F-Score given the true labels, predicted labels, and the Beta value of a binary classification task. The results should be rounded to three decimal places.
    Special Case:

    If the denominator is zero, the F-Score should be set to 0.0 to avoid division by zero.
	"""
	tp = np.sum((y_true == 1) & (y_pred == 1))
	fp = np.sum((y_true == 0) & (y_pred == 1))
	fn = np.sum((y_true == 1) & (y_pred == 0))

	if tp + fp == 0:
		precision = 0.0
	else:
		precision = tp / (tp + fp)

	if tp + fn == 0:
		recall = 0.0
	else:
		recall = tp / (tp + fn)

	denominator = (beta**2 * precision) + recall
	if denominator == 0:
		return 0.0

	f_score_val = (1 + beta**2) * (precision * recall) / denominator
	return round(f_score_val, 3)
