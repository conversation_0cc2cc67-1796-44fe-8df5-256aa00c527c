import numpy as np

def l1_regularization_gradient_descent(X: np.array, y: np.array, alpha: float = 0.1, learning_rate: float = 0.01, max_iter: int = 1000, tol: float = 1e-4) -> tuple:
    """implement the Lasso Regression algorithm using Gradient Descent. Lasso Regression (L1 Regularization) adds a penalty equal to the absolute value of the coefficients to the loss function. Your task is to update the weights and bias iteratively using the gradient of the loss function and the L1 penalty.

    The objective function of Lasso Regression is:
    J(w,b)=12n∑i=1n(yi−(∑j=1pXijwj+b))2+α∑j=1p∣wj∣
    J(w,b)=2n1​i=1∑n​(yi​−(j=1∑p​Xij​wj​+b))2+αj=1∑p​∣wj​∣

    Where:

        yiyi​ is the actual value for the ii-th sample
        y^i=∑j=1pXijwj+by^​i​=∑j=1p​Xij​wj​+b is the predicted value for the ii-th sample
        wjwj​ is the weight associated with the jj-th feature
        αα is the regularization parameter
        bb is the bias

    Your task is to use the L1 penalty to shrink some of the feature coefficients to zero during gradient descent, thereby helping with feature selection.
    Example:
    Input:

    import numpy as np

    X = np.array([[0, 0], [1, 1], [2, 2]])
    y = np.array([0, 1, 2])

    alpha = 0.1
    weights, bias = l1_regularization_gradient_descent(X, y, alpha=alpha, learning_rate=0.01, max_iter=1000)

    Output:

    (weights,bias)
    (array([float, float]), float)

    Reasoning:

    The Lasso Regression algorithm is used to optimize the weights and bias for the given data. The weights are adjusted to minimize the loss function with the L1 penalty.
    """
	n_samples, n_features = X.shape

	weights = np.zeros(n_features)
	bias = 0

	for i in range(max_iter):
		# Calculate predictions
		predictions = X @ weights + bias

		# Calculate errors
		errors = predictions - y

		# Calculate gradients
		# Gradient for weights (data term)
		grad_w_data = (1 / n_samples) * X.T @ errors

		# Gradient for weights (L1 regularization term)
		grad_w_l1 = alpha * np.sign(weights)

		# Total gradient for weights
		grad_w = grad_w_data + grad_w_l1

		# Gradient for bias
		grad_b = (1 / n_samples) * np.sum(errors)

		# Store previous weights and bias for convergence check
		prev_weights = np.copy(weights)
		prev_bias = bias

		# Update weights and bias
		weights -= learning_rate * grad_w
		bias -= learning_rate * grad_b

		# Check for convergence
		if np.sum(np.abs(grad_w)) < tol:
			break

	return weights, bias
