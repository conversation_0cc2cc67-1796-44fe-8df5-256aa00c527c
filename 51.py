def OSA(source: str, target: str) -> int:
    """implement a function that calculates the Optimal String Alignment (OSA) distance between two given strings. The OSA distance represents the minimum number of edits required to transform one string into another. The allowed edit operations are:

        Insert a character
        Delete a character
        Substitute a character
        Transpose two adjacent characters

    Each of these operations costs 1 unit.

    Your task is to find the minimum number of edits needed to convert the first string (s1) into the second string (s2).

    For example, the OSA distance between the strings caper and acer is 2: one deletion (removing "p") and one transposition (swapping "a" and "c").
    Example:
    Input:

    source = "butterfly"
    target = "dragonfly"

    distance = OSA(source, target)
    print(distance)

    Output:

    6

    Reasoning:

    The OSA distance between the strings "butterfly" and "dragonfly" is 6. The minimum number of edits required to transform the source string into the target string is 6.
    """
    n = len(source)
    m = len(target)

    dp = [[0] * (m + 1) for _ in range(n + 1)]

    # Initialize base cases
    for i in range(n + 1):
        dp[i][0] = i
    for j in range(m + 1):
        dp[0][j] = j

    for i in range(1, n + 1):
        for j in range(1, m + 1):
            cost = 0 if source[i - 1] == target[j - 1] else 1

            # Deletion, Insertion, Substitution
            dp[i][j] = min(
                dp[i - 1][j] + 1,  # Deletion
                dp[i][j - 1] + 1,  # Insertion
                dp[i - 1][j - 1] + cost,  # Substitution
            )

            # Transposition
            if i >= 2 and j >= 2 and source[i - 1] == target[j - 2] and source[i - 2] == target[j - 1]:
                dp[i][j] = min(dp[i][j], dp[i - 2][j - 2] + 1)

    return dp[n][m]
