import numpy as np

def calculate_correlation_matrix(X, Y=None):
	"""Write a Python function to calculate the correlation matrix for a given dataset. The function should take in a 2D numpy array X and an optional 2D numpy array Y. If Y is not provided, the function should calculate the correlation matrix of X with itself. It should return the correlation matrix as a 2D numpy array.
    
	Example:
    Input:

    X = np.array([[1, 2],
                      [3, 4],
                      [5, 6]])
        output = calculate_correlation_matrix(X)
        print(output)

    Output:

    # [[1. 1.]
        #  [1. 1.]]

    Reasoning:

    The function calculates the correlation matrix for the dataset X. In this example, the correlation between the two features is 1, indicating a perfect linear relationship.
    """
	# If Y is not provided, calculate correlation matrix of X with itself
	if Y is None:
		Y = X
	
	# Ensure X and Y are 2D arrays
	X = np.asarray(X)
	Y = np.asarray(Y)
	
	if X.ndim == 1:
		X = X.reshape(-1, 1)
	if Y.ndim == 1:
		Y = Y.reshape(-1, 1)
	
	# Calculate means
	X_mean = np.mean(X, axis=0)
	Y_mean = np.mean(Y, axis=0)
	
	# Center the data
	X_centered = X - X_mean
	Y_centered = Y - Y_mean
	
	# Calculate covariance matrix
	n = X.shape[0]
	cov_matrix = np.dot(X_centered.T, Y_centered) / (n - 1)
	
	# Calculate standard deviations
	X_std = np.std(X, axis=0, ddof=1)
	Y_std = np.std(Y, axis=0, ddof=1)
	
	# Calculate correlation matrix
	# Avoid division by zero
	X_std_matrix = X_std.reshape(-1, 1)
	Y_std_matrix = Y_std.reshape(1, -1)
	
	# Create outer product of standard deviations
	std_outer = np.outer(X_std, Y_std)
	
	# Avoid division by zero
	correlation_matrix = np.where(std_outer != 0, cov_matrix / std_outer, 0)
	
	return correlation_matrix

# Test the function
if __name__ == "__main__":
	X = np.array([[1, 2],
				  [3, 4],
				  [5, 6]])
	output = calculate_correlation_matrix(X)
	print(output)