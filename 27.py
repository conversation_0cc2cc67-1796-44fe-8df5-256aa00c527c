import numpy as np

def transform_basis(B: list[list[int]], C: list[list[int]]) -> list[list[float]]:
	"""Given basis vectors in two different bases B and C for R^3, write a Python function to compute the transformation matrix P from basis B to C.
    
	Example:
    Input:

    B = [[1, 0, 0], 
                 [0, 1, 0], 
                 [0, 0, 1]]
    C = [[1, 2.3, 3], 
                 [4.4, 25, 6], 
                 [7.4, 8, 9]]

    Output:

    [[-0.6772, -0.0126, 0.2342],
     [-0.0184, 0.0505, -0.0275],
     [0.5732, -0.0345, -0.0569]]

    Reasoning:

    The transformation matrix P from basis B to C can be found using matrix operations involving the inverse of matrix C.
    """
    # Convert lists to numpy arrays for matrix operations
    B_matrix = np.array(B, dtype=float)
    C_matrix = np.array(C, dtype=float)
    
    # Compute the inverse of matrix C
    C_inv = np.linalg.inv(C_matrix)
    
    # Compute the transformation matrix P = C^(-1) * B
    P = C_inv @ B_matrix
    
    # Convert back to list of lists and round to 4 decimal places
    P_list = P.tolist()
    P_rounded = [[round(val, 4) for val in row] for row in P_list]
    
    return P_rounded