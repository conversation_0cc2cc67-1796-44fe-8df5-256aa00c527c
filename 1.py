def matrix_dot_vector(a: list[list[int|float]], b: list[int|float]) -> list[int|float]:
	# Return a list where each element is the dot product of a row of 'a' with 'b'.
	# If the number of columns in 'a' does not match the length of 'b', return -1.
	if not a or not b:  # Check if either input is empty
		return -1
	
	# Check if all rows in matrix a have the same length as vector b
	if any(len(row) != len(b) for row in a):
		return -1
	
	# Calculate dot product for each row
	result = []
	for row in a:
		dot_product = sum(x * y for x, y in zip(row, b))
		result.append(dot_product)
	
	return result