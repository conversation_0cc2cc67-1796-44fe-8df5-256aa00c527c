import numpy as np

def solve_jacobi(A: np.n<PERSON><PERSON>, b: np.n<PERSON><PERSON>, n: int) -> np.ndarray:
    """Uses the Jacobi method to solve a system of linear equations given by Ax = b.
    
    Args:
        A: Coefficient matrix (must be square and have non-zero diagonal elements)
        b: Right-hand side vector
        n: Maximum number of iterations
    
    Returns:
        np.ndarray: Approximate solution x
    
    Raises:
        ValueError: If inputs are invalid or matrix A has zero diagonal elements
    """
    # Input validation
    if not isinstance(A, np.ndarray) or not isinstance(b, np.ndarray):
        raise ValueError("A and b must be numpy arrays")
    
    if A.shape[0] != A.shape[1]:
        raise ValueError("Matrix A must be square")
    
    if A.shape[0] != len(b):
        raise ValueError("Dimensions of A and b must match")
    
    if np.any(np.diag(A) == 0):
        raise ValueError("Matrix A must have non-zero diagonal elements")
    
    # Initialize solution vector with zeros
    x = np.zeros_like(b, dtype=float)
    new_x = np.zeros_like(b, dtype=float)
    
    # Perform iterations
    for _ in range(n):
        # For each equation
        for i in range(len(b)):
            # Calculate sum of a_ij * x[j] for j != i using vectorized operations
            sum_ax = np.dot(A[i, :i], x[:i]) + np.dot(A[i, i+1:], x[i+1:])
            
            # Calculate new x[i] using Jacobi formula with protection against division by zero
            diag_element = A[i, i]
            if abs(diag_element) < 1e-10:  # Check for near-zero diagonal elements
                raise ValueError(f"Near-zero diagonal element at position {i}")
            
            new_x[i] = (b[i] - sum_ax) / diag_element
        
        # Update solution vector
        x = new_x.copy()
    
    # Round to 4 decimal places
    return np.round(x, decimals=4)


if __name__ == "__main__":
    print(solve_jacobi(np.array([[5, -2, 3], [-3, 9, 1], [2, -1, -7]]), np.array([-1, 2, 3]),2))