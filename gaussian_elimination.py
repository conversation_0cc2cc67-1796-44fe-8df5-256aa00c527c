import numpy as np # Import the NumPy library for numerical operations

def gaussian_elimination(A, b):
	"""
	Solves the system Ax = b using Gaussian Elimination with partial pivoting.
    
	:param A: Coefficient matrix
	:param b: Right-hand side vector
	:return: Solution vector x

    implement the Gaussian Elimination method, which transforms a system of linear equations into an upper triangular matrix. This method can then be used to solve for the variables using backward substitution.

    Write a function gaussian_elimination(A, b) that performs Gaussian Elimination with partial pivoting to solve the system (Ax = b).

    The function should return the solution vector (x).
    Example:
    Input:

    A = np.array([[2,8,4], [2,5,1], [4,10,-1]], dtype=float)
    b = np.array([2,5,1], dtype=float)

    print(gaussian_elimination(A, b))

    Output:

    [11.0, -4.0, 3.0]

    Reasoning:

    The Gaussian Elimination method transforms the system of equations into an upper triangular matrix and then uses backward substitution to solve for the variables.

	"""
	n = len(b) # Get the number of equations (and variables)
	# Create an augmented matrix by concatenating matrix A and vector b
	Ab = np.concatenate((A, b.reshape(n, 1)), axis=1) 

	# Loop through each row for forward elimination
	for i in range(n):
		# Partial Pivoting: Find the row with the largest absolute value in the current column (pivot column)
		max_row = i 
		for k in range(i + 1, n):
			if abs(Ab[k, i]) > abs(Ab[max_row, i]):
				max_row = k
		# Swap the current row with the row found in partial pivoting
		Ab[[i, max_row]] = Ab[[max_row, i]] 

		# Eliminate: Perform row operations to create zeros below the pivot
		for k in range(i + 1, n):
			factor = Ab[k, i] / Ab[i, i] # Calculate the factor to multiply the pivot row by
			Ab[k, i:] = Ab[k, i:] - factor * Ab[i, i:] # Subtract the scaled pivot row from the current row

	# Backward Substitution: Solve for variables from the last row up
	x = np.zeros(n) # Initialize the solution vector x with zeros
	for i in range(n - 1, -1, -1): # Loop from the last row to the first
		# Calculate the value of the current variable using previously solved variables
		x[i] = (Ab[i, n] - np.dot(Ab[i, i+1:n], x[i+1:n])) / Ab[i, i] 

	return x # Return the solution vector x
