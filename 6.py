def calculate_eigenvalues(matrix: list[list[float|int]]) -> list[float]:
	"""The eigenvalues of the matrix are calculated using the characteristic equation of the matrix, which for a 2x2 matrix is λ2−trace(A)λ+det(A)=0λ2−trace(A)λ+det(A)=0, where λλ are the eigenvalues
	
	尽量不用numpy库
	"""
	# Calculate trace (sum of diagonal elements)
	trace = matrix[0][0] + matrix[1][1]
	
	# Calculate determinant (ad - bc for matrix [[a,b],[c,d]])
	det = matrix[0][0] * matrix[1][1] - matrix[0][1] * matrix[1][0]
	
	# Calculate discriminant
	discriminant = trace * trace - 4 * det
	
	# Calculate eigenvalues using quadratic formula
	if discriminant >= 0:
		# Real eigenvalues
		lambda1 = (trace + (discriminant ** 0.5)) / 2
		lambda2 = (trace - (discriminant ** 0.5)) / 2
		return [lambda1, lambda2]
	else:
		# Complex eigenvalues (return real and imaginary parts)
		real_part = trace / 2
		imag_part = ((-discriminant) ** 0.5) / 2
		return [complex(real_part, imag_part), complex(real_part, -imag_part)]